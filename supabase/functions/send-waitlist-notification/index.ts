import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { Resend } from "npm:resend@2.0.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const resend = new Resend(Deno.env.get("RESEND_API_KEY"));
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const {
      user_id,
      entry_id,
      club_id,
      court_id,
      booking_date,
      start_time,
      end_time,
      player_name,
      partner_name
    } = await req.json();

    console.log('Sending waitlist notification for:', { user_id, entry_id, booking_date, start_time });

    // Get user profile for email
    const { data: profile, error: profileError } = await supabaseClient
      .from('profiles')
      .select('first_name, last_name, email')
      .eq('id', user_id)
      .single();

    if (profileError || !profile) {
      console.error('Error fetching user profile:', profileError);
      throw new Error('User profile not found');
    }

    // Get court information
    const { data: court, error: courtError } = await supabaseClient
      .from('courts')
      .select('number')
      .eq('id', court_id)
      .single();

    if (courtError || !court) {
      console.error('Error fetching court:', courtError);
      throw new Error('Court not found');
    }

    // Get club information including SMTP settings
    const { data: club, error: clubError } = await supabaseClient
      .from('clubs')
      .select('name, settings, timezone')
      .eq('id', club_id)
      .single();

    if (clubError || !club) {
      console.error('Error fetching club:', clubError);
      throw new Error('Club not found');
    }

    // CRITICAL: Use club timezone for date formatting
    const clubTimezone = club.timezone || 'Europe/Berlin';
    const formattedDate = new Date(booking_date).toLocaleDateString('de-DE', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: clubTimezone
    });

    const formattedTime = `${start_time} - ${end_time} Uhr`;
    const courtName = `Platz ${court.number}`;

    // Use club-specific email service with service role authorization
    // Create a service token for internal system operations
    const serviceToken = `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`;

    const emailResponse = await supabaseClient.functions.invoke('club-email-service', {
      headers: {
        'Authorization': serviceToken
      },
      body: {
        clubId: club_id,
        to: profile.email,
        subject: `🎾 Platz verfügbar - Ihre Warteliste bei ${club.name}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #059669;">🎾 Platz verfügbar!</h1>
            
            <p>Hallo ${profile.first_name} ${profile.last_name},</p>
            
            <p>großartige Neuigkeiten! Ein Platz ist für Ihren gewünschten Zeitraum verfügbar geworden:</p>
            
            <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #0891b2;">Buchungsdetails</h3>
              <p><strong>Datum:</strong> ${formattedDate}</p>
              <p><strong>Zeit:</strong> ${formattedTime}</p>
              <p><strong>Platz:</strong> ${courtName}</p>
              <p><strong>Spieler:</strong> ${player_name}${partner_name ? ` & ${partner_name}` : ''}</p>
            </div>
            
            <p style="font-size: 16px; color: #dc2626;">
              <strong>⏰ Wichtig:</strong> Dieser Platz wird nach dem "First-Come, First-Serve" Prinzip vergeben. 
              Loggen Sie sich schnell ein, um den Platz zu buchen!
            </p>
            
            <a href="${Deno.env.get('SUPABASE_URL')?.replace('supabase.co', 'app')}/member/bookings" 
               style="display: inline-block; background-color: #059669; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 6px; font-weight: bold; margin: 20px 0;">
              Jetzt buchen
            </a>
            
            <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
              Diese E-Mail wurde automatisch generiert. Sie erhalten diese Nachricht, weil Sie sich auf die Warteliste 
              für diesen Zeitraum gesetzt haben.
            </p>
            
            <p style="color: #6b7280; font-size: 14px;">
              Viele Grüße,<br>
              Ihr ${club.name} Team
            </p>
          </div>
        `
      }
    });

    if (emailResponse.error) {
      throw emailResponse.error;
    }

    console.log('Waitlist notification sent via club email service:', emailResponse.data);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Notification sent successfully',
        email: profile.email,
        club: club.name
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );

  } catch (error: any) {
    console.error('Error in send-waitlist-notification:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});